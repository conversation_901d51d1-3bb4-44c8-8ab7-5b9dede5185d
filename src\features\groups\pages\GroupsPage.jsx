import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Plus,
  Users,
  DollarSign,
  UserPlus,
  MoreVertical,
  Edit,
  Trash2,
  Share
} from 'lucide-react'
import { useGroups } from '../../../contexts/GroupContext'
import { useAuth } from '../../../contexts/AuthContext'
import { Button, FloatingActionButton } from '../../../components/ui/Button'
import { SkeletonCard } from '../../../components/ui/SkeletonLoader'
import { CreateGroupModal } from '../components/CreateGroupModal'
import { EditGroupModal } from '../components/EditGroupModal'
import { ConfirmModal } from '../../../components/ui/Modal'
import { GroupCard } from '../components/GroupCard'
import { StaggerContainer, StaggerItem } from '../../../components/ui/PageTransition'

export function GroupsPage() {
  const { groups, isLoading, createGroup, deleteGroup } = useGroups()
  const { isGuest } = useAuth()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [editingGroup, setEditingGroup] = useState(null)
  const [deletingGroup, setDeletingGroup] = useState(null)

  const handleCreateGroup = async (groupData) => {
    try {
      await createGroup(groupData)
      setShowCreateModal(false)
    } catch (error) {
      console.error('Failed to create group:', error)
    }
  }

  const handleDeleteGroup = async () => {
    if (deletingGroup) {
      try {
        await deleteGroup(deletingGroup.id)
        setDeletingGroup(null)
      } catch (error) {
        console.error('Failed to delete group:', error)
      }
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="md:flex md:items-center md:justify-between">
          <div className="min-w-0 flex-1">
            <div className="h-8 bg-gray-200 rounded w-32 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-64"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {Array.from({ length: 6 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="md:flex md:items-center md:justify-between"
      >
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Groups
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            {isGuest
              ? "Manage your expense groups. Data is stored locally in guest mode."
              : "Create and manage groups to split expenses with friends and family."
            }
          </p>
        </div>
        <div className="mt-4 flex md:ml-4 md:mt-0">
          <Button
            onClick={() => setShowCreateModal(true)}
            leftIcon={<Plus className="w-5 h-5" />}
          >
            Create Group
          </Button>
        </div>
      </motion.div>

      {/* Groups Grid */}
      {groups.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-center py-12"
        >
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No groups</h3>
          <p className="mt-1 text-sm text-gray-500">
            Get started by creating your first expense group.
          </p>
          <div className="mt-6">
            <Button
              onClick={() => setShowCreateModal(true)}
              leftIcon={<Plus className="w-5 h-5" />}
            >
              Create Group
            </Button>
          </div>
        </motion.div>
      ) : (
        <StaggerContainer className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {groups.map((group, index) => (
            <StaggerItem key={group.id}>
              <GroupCard
                group={group}
                index={index}
                onEdit={() => setEditingGroup(group)}
                onDelete={() => setDeletingGroup(group)}
              />
            </StaggerItem>
          ))}
        </StaggerContainer>
      )}

      {/* Floating Action Button for Mobile */}
      <FloatingActionButton onClick={() => setShowCreateModal(true)}>
        <Plus className="w-6 h-6" />
      </FloatingActionButton>

      {/* Modals */}
      <CreateGroupModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateGroup}
      />

      <EditGroupModal
        isOpen={!!editingGroup}
        group={editingGroup}
        onClose={() => setEditingGroup(null)}
      />

      <ConfirmModal
        isOpen={!!deletingGroup}
        onClose={() => setDeletingGroup(null)}
        onConfirm={handleDeleteGroup}
        title="Delete Group"
        message={`Are you sure you want to delete "${deletingGroup?.name}"? This action cannot be undone and will remove all associated expenses.`}
        confirmLabel="Delete"
        variant="danger"
      />
    </div>
  )
}
