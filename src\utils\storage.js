/**
 * Enhanced localStorage utilities for Guest Mode and data persistence
 */

const STORAGE_KEYS = {
  GROUPS: 'easysplit_groups',
  EXPENSES: 'easysplit_expenses',
  BALANCES: 'easysplit_balances',
  USER_PREFERENCES: 'easysplit_preferences',
  DEBT_REMINDERS: 'easysplit_debt_reminders',
  ACTIVITY_LOG: 'easysplit_activity_log',
  APP_STATE: 'easysplit_app_state'
}

/**
 * Save data to localStorage with error handling
 */
export const saveToStorage = (key, data) => {
  try {
    const serializedData = JSON.stringify({
      data,
      timestamp: new Date().toISOString(),
      version: '1.0'
    })
    localStorage.setItem(key, serializedData)
    return true
  } catch (error) {
    console.error('Failed to save to localStorage:', error)
    return false
  }
}

/**
 * Load data from localStorage with error handling and validation
 */
export const loadFromStorage = (key, defaultValue = null) => {
  try {
    const item = localStorage.getItem(key)
    if (!item) return defaultValue

    const parsed = JSON.parse(item)
    
    // Validate data structure
    if (parsed && typeof parsed === 'object' && parsed.data !== undefined) {
      return parsed.data
    }
    
    // Handle legacy data format
    return parsed || defaultValue
  } catch (error) {
    console.error('Failed to load from localStorage:', error)
    return defaultValue
  }
}

/**
 * Remove data from localStorage
 */
export const removeFromStorage = (key) => {
  try {
    localStorage.removeItem(key)
    return true
  } catch (error) {
    console.error('Failed to remove from localStorage:', error)
    return false
  }
}

/**
 * Clear all app data from localStorage
 */
export const clearAllStorage = () => {
  try {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorage.removeItem(key)
    })
    return true
  } catch (error) {
    console.error('Failed to clear localStorage:', error)
    return false
  }
}

/**
 * Get storage usage information
 */
export const getStorageInfo = () => {
  try {
    let totalSize = 0
    const items = {}
    
    Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
      const item = localStorage.getItem(key)
      const size = item ? new Blob([item]).size : 0
      items[name] = {
        key,
        size,
        exists: !!item
      }
      totalSize += size
    })
    
    return {
      totalSize,
      items,
      available: true
    }
  } catch (error) {
    console.error('Failed to get storage info:', error)
    return {
      totalSize: 0,
      items: {},
      available: false
    }
  }
}

/**
 * Initialize default app state for Guest Mode
 */
export const initializeGuestMode = () => {
  const defaultState = {
    isGuestMode: true,
    userId: `guest_${Date.now()}`,
    userName: 'Guest User',
    preferences: {
      currency: 'USD',
      theme: 'light',
      notifications: true,
      language: 'en'
    },
    onboardingCompleted: false
  }
  
  const existingState = loadFromStorage(STORAGE_KEYS.APP_STATE)
  if (!existingState) {
    saveToStorage(STORAGE_KEYS.APP_STATE, defaultState)
    return defaultState
  }
  
  return { ...defaultState, ...existingState }
}

/**
 * Export all data for backup or sync
 */
export const exportAllData = () => {
  try {
    const exportData = {}
    
    Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
      const data = loadFromStorage(key)
      if (data) {
        exportData[name] = data
      }
    })
    
    return {
      exportData,
      timestamp: new Date().toISOString(),
      version: '1.0'
    }
  } catch (error) {
    console.error('Failed to export data:', error)
    return null
  }
}

/**
 * Import data from backup or sync
 */
export const importAllData = (importData, mergeStrategy = 'replace') => {
  try {
    if (!importData || typeof importData !== 'object') {
      throw new Error('Invalid import data')
    }
    
    Object.entries(importData.exportData || importData).forEach(([name, data]) => {
      const key = STORAGE_KEYS[name]
      if (key) {
        if (mergeStrategy === 'merge') {
          const existing = loadFromStorage(key, {})
          const merged = { ...existing, ...data }
          saveToStorage(key, merged)
        } else {
          saveToStorage(key, data)
        }
      }
    })
    
    return true
  } catch (error) {
    console.error('Failed to import data:', error)
    return false
  }
}

/**
 * Sync data with cloud storage (placeholder for future implementation)
 */
export const syncWithCloud = async (userData) => {
  // This will be implemented when user signs in
  console.log('Cloud sync not implemented in Guest Mode')
  return { success: false, message: 'Sign in required for cloud sync' }
}

export { STORAGE_KEYS }
