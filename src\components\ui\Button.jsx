import React from 'react'
import { motion } from 'framer-motion'
import { LoadingSpinner } from './LoadingSpinner'

// Animation variants for buttons
const buttonVariants = {
  initial: { scale: 1 },
  hover: {
    scale: 1.02,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  },
  tap: {
    scale: 0.98,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
}

const iconVariants = {
  initial: { rotate: 0 },
  hover: {
    rotate: 5,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 10
    }
  }
}

const variants = {
  primary: 'bg-primary-600 hover:bg-primary-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5',
  secondary: 'bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-md hover:shadow-lg transition-all duration-200',
  danger: 'bg-danger-600 hover:bg-danger-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5',
  success: 'bg-success-600 hover:bg-success-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5',
  warning: 'bg-warning-600 hover:bg-warning-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5',
  ghost: 'hover:bg-gray-100 text-gray-700 transition-all duration-200',
  link: 'text-primary-600 hover:text-primary-700 underline-offset-4 hover:underline transition-all duration-200'
}

const sizes = {
  xs: 'px-2.5 py-1.5 text-xs',
  sm: 'px-3 py-2 text-sm',
  md: 'px-4 py-2 text-sm',
  lg: 'px-4 py-2 text-base',
  xl: 'px-6 py-3 text-base'
}

export function Button({
  children,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  className = '',
  onClick,
  type = 'button',
  ...props
}) {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variantClasses = variants[variant] || variants.primary
  const sizeClasses = sizes[size] || sizes.md
  const widthClasses = fullWidth ? 'w-full' : ''
  
  const isDisabled = disabled || loading

  return (
    <motion.button
      variants={buttonVariants}
      initial="initial"
      whileHover={!isDisabled ? "hover" : "initial"}
      whileTap={!isDisabled ? "tap" : "initial"}
      className={`${baseClasses} ${variantClasses} ${sizeClasses} ${widthClasses} ${className}`}
      disabled={isDisabled}
      onClick={onClick}
      type={type}
      {...props}
    >
      {loading && (
        <LoadingSpinner 
          size={size === 'xs' || size === 'sm' ? 'sm' : 'md'} 
          className="mr-2" 
        />
      )}
      
      {!loading && leftIcon && (
        <motion.span
          className="mr-2 flex-shrink-0"
          variants={iconVariants}
          initial="initial"
          whileHover="hover"
        >
          {leftIcon}
        </motion.span>
      )}

      <span className={loading ? 'opacity-70' : ''}>
        {children}
      </span>

      {!loading && rightIcon && (
        <motion.span
          className="ml-2 flex-shrink-0"
          variants={iconVariants}
          initial="initial"
          whileHover="hover"
        >
          {rightIcon}
        </motion.span>
      )}
    </motion.button>
  )
}

// Specialized button components
export function IconButton({ 
  children, 
  variant = 'ghost', 
  size = 'md',
  className = '',
  ...props 
}) {
  const iconSizes = {
    xs: 'w-6 h-6 p-1',
    sm: 'w-8 h-8 p-1.5',
    md: 'w-10 h-10 p-2',
    lg: 'w-12 h-12 p-2.5',
    xl: 'w-14 h-14 p-3'
  }
  
  return (
    <Button
      variant={variant}
      className={`${iconSizes[size]} rounded-full ${className}`}
      {...props}
    >
      {children}
    </Button>
  )
}

export function FloatingActionButton({ 
  children, 
  className = '',
  ...props 
}) {
  return (
    <motion.button
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      className={`fixed bottom-6 right-6 w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 z-50 ${className}`}
      {...props}
    >
      {children}
    </motion.button>
  )
}
