import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import {
  Plus,
  Receipt,
  Users,
  BarChart3,
  Settings,
  Download,
  Upload,
  Smartphone
} from 'lucide-react'

const actionVariants = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { 
    opacity: 1, 
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  },
  hover: {
    scale: 1.05,
    y: -2,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  },
  tap: {
    scale: 0.95
  }
}

const containerVariants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

export function QuickActions({ onCreateGroup, onAddExpense }) {
  const navigate = useNavigate()

  const quickActions = [
    {
      id: 'create-group',
      title: 'Create Group',
      description: 'Start a new expense group',
      icon: Users,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: onCreateGroup
    },
    {
      id: 'add-expense',
      title: 'Add Expense',
      description: 'Quick expense entry',
      icon: Receipt,
      color: 'bg-green-500 hover:bg-green-600',
      action: onAddExpense
    },
    {
      id: 'view-analytics',
      title: 'Analytics',
      description: 'View spending insights',
      icon: BarChart3,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => navigate('/analytics')
    },
    {
      id: 'settings',
      title: 'Settings',
      description: 'App preferences',
      icon: Settings,
      color: 'bg-gray-500 hover:bg-gray-600',
      action: () => navigate('/settings')
    }
  ]

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Quick Actions
        </h2>
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          className="w-6 h-6 text-primary-500"
        >
          <Smartphone className="w-6 h-6" />
        </motion.div>
      </div>

      <motion.div
        variants={containerVariants}
        initial="initial"
        animate="animate"
        className="grid grid-cols-2 gap-4"
      >
        {quickActions.map((action) => (
          <motion.button
            key={action.id}
            variants={actionVariants}
            whileHover="hover"
            whileTap="tap"
            onClick={action.action}
            className="group relative overflow-hidden rounded-xl p-4 text-left transition-all duration-200"
          >
            {/* Background gradient */}
            <div className={`absolute inset-0 ${action.color} opacity-10 group-hover:opacity-20 transition-opacity`} />
            
            {/* Content */}
            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg ${action.color} text-white mb-3`}>
                <action.icon className="w-5 h-5" />
              </div>
              
              <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                {action.title}
              </h3>
              
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {action.description}
              </p>
            </div>

            {/* Hover effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
              initial={{ x: '-100%' }}
              whileHover={{ x: '100%' }}
              transition={{ duration: 0.6 }}
            />
          </motion.button>
        ))}
      </motion.div>

      {/* Additional actions */}
      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
            Data Management
          </span>
          <div className="flex space-x-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title="Export Data"
            >
              <Download className="w-4 h-4" />
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              title="Import Data"
            >
              <Upload className="w-4 h-4" />
            </motion.button>
          </div>
        </div>
      </div>
    </div>
  )
}
