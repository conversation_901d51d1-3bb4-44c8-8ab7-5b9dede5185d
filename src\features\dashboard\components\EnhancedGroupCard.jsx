import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import {
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown,
  ArrowRight,
  Calendar,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { formatCurrency } from '../../../utils/formatters'
import { useGuestMode } from '../../../contexts/GuestModeContext'

const cardVariants = {
  initial: { 
    opacity: 0, 
    y: 20, 
    scale: 0.95 
  },
  animate: { 
    opacity: 1, 
    y: 0, 
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  },
  hover: {
    y: -8,
    scale: 1.02,
    boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  },
  tap: {
    scale: 0.98
  }
}

const iconVariants = {
  initial: { scale: 1, rotate: 0 },
  hover: { 
    scale: 1.1, 
    rotate: 5,
    transition: { 
      type: "spring", 
      stiffness: 400, 
      damping: 10 
    }
  }
}

export function EnhancedGroupCard({ group, index = 0 }) {
  const navigate = useNavigate()
  const { preferences } = useGuestMode()

  // Calculate group statistics
  const totalExpenses = group.expenses?.reduce((sum, expense) => sum + expense.amount, 0) || 0
  const memberCount = group.members?.length || 0
  const recentActivity = group.lastActivity || group.createdAt
  const hasDebts = group.balances && Object.keys(group.balances).length > 0

  // Calculate debt summary
  const debtSummary = React.useMemo(() => {
    if (!group.balances) return { youOwe: 0, youAreOwed: 0, isSettled: true }
    
    let youOwe = 0
    let youAreOwed = 0
    
    Object.entries(group.balances).forEach(([member, balance]) => {
      if (balance > 0) {
        youAreOwed += balance
      } else {
        youOwe += Math.abs(balance)
      }
    })
    
    return {
      youOwe,
      youAreOwed,
      isSettled: youOwe === 0 && youAreOwed === 0
    }
  }, [group.balances])

  const handleCardClick = () => {
    navigate(`/groups/${group.id}`)
  }

  const getStatusColor = () => {
    if (debtSummary.isSettled) return 'text-green-600 bg-green-100'
    if (debtSummary.youOwe > debtSummary.youAreOwed) return 'text-red-600 bg-red-100'
    return 'text-blue-600 bg-blue-100'
  }

  const getStatusIcon = () => {
    if (debtSummary.isSettled) return CheckCircle
    if (debtSummary.youOwe > debtSummary.youAreOwed) return TrendingDown
    return TrendingUp
  }

  const StatusIcon = getStatusIcon()

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      whileTap="tap"
      onClick={handleCardClick}
      className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden cursor-pointer group"
    >
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
              {group.name}
            </h3>
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
              <Users className="w-4 h-4 mr-1" />
              <span>{memberCount} member{memberCount !== 1 ? 's' : ''}</span>
            </div>
          </div>
          
          <motion.div
            variants={iconVariants}
            className={`p-2 rounded-full ${getStatusColor()}`}
          >
            <StatusIcon className="w-5 h-5" />
          </motion.div>
        </div>

        {/* Total Spent */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
              Total Spent
            </span>
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              {formatCurrency(totalExpenses, preferences.currency)}
            </span>
          </div>
        </div>

        {/* Debt Summary */}
        {!debtSummary.isSettled && (
          <div className="space-y-2">
            {debtSummary.youOwe > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-red-600 dark:text-red-400">You owe:</span>
                <span className="font-semibold text-red-600 dark:text-red-400">
                  {formatCurrency(debtSummary.youOwe, preferences.currency)}
                </span>
              </div>
            )}
            {debtSummary.youAreOwed > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-green-600 dark:text-green-400">You are owed:</span>
                <span className="font-semibold text-green-600 dark:text-green-400">
                  {formatCurrency(debtSummary.youAreOwed, preferences.currency)}
                </span>
              </div>
            )}
          </div>
        )}

        {debtSummary.isSettled && (
          <div className="flex items-center text-sm text-green-600 dark:text-green-400">
            <CheckCircle className="w-4 h-4 mr-1" />
            <span>All settled up!</span>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-600">
        <div className="flex items-center justify-between">
          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
            <Calendar className="w-3 h-3 mr-1" />
            <span>
              {recentActivity ? 
                `Updated ${new Date(recentActivity).toLocaleDateString()}` : 
                'No recent activity'
              }
            </span>
          </div>
          
          <motion.div
            className="flex items-center text-primary-600 dark:text-primary-400 text-sm font-medium"
            whileHover={{ x: 4 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <span>View Details</span>
            <ArrowRight className="w-4 h-4 ml-1" />
          </motion.div>
        </div>
      </div>
    </motion.div>
  )
}

// Empty state component for when no groups exist
export function EmptyGroupsState({ onCreateGroup }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="text-center py-12"
    >
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        className="w-24 h-24 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-6"
      >
        <Users className="w-12 h-12 text-primary-600 dark:text-primary-400" />
      </motion.div>
      
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        No groups yet
      </h3>
      <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md mx-auto">
        Create your first group to start splitting expenses with friends, family, or roommates.
      </p>
      
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          onClick={onCreateGroup}
          leftIcon={<Plus className="w-5 h-5" />}
          size="lg"
        >
          Create Your First Group
        </Button>
      </motion.div>
    </motion.div>
  )
}
