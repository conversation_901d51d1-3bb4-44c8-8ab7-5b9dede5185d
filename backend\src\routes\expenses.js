const express = require('express');
const { protect } = require('../middleware/auth');

const router = express.Router();

// Placeholder routes - to be implemented
router.get('/', protect, (req, res) => {
  res.json({ success: true, message: 'Expenses routes - Coming soon!' });
});

router.post('/', protect, (req, res) => {
  res.json({ success: true, message: 'Create expense - Coming soon!' });
});

router.get('/:id', protect, (req, res) => {
  res.json({ success: true, message: 'Get expense - Coming soon!' });
});

router.put('/:id', protect, (req, res) => {
  res.json({ success: true, message: 'Update expense - Coming soon!' });
});

router.delete('/:id', protect, (req, res) => {
  res.json({ success: true, message: 'Delete expense - Coming soon!' });
});

module.exports = router;
