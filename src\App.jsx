import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Toaster } from 'react-hot-toast'
import { AuthProvider } from './contexts/AuthContext'
import { GroupProvider } from './contexts/GroupContext'
import { ExpenseProvider } from './contexts/ExpenseContext'
import { BalanceProvider } from './contexts/BalanceContext'
import { GuestModeProvider } from './contexts/GuestModeContext'
import { ProtectedRoute } from './components/auth/ProtectedRoute'
import { Layout } from './components/layout/Layout'
import ErrorBoundary from './components/ErrorBoundary'
import { PWAInstallPrompt } from './components/PWAInstallPrompt'
import { GuestModeApp } from './components/GuestModeApp'

// Auth Pages
import { LoginPage } from './features/auth/pages/LoginPage'
import { SignupPage } from './features/auth/pages/SignupPage'
import { ForgotPasswordPage } from './features/auth/pages/ForgotPasswordPage'

// Main App Pages
import { DashboardPage } from './features/dashboard/pages/DashboardPage'
import { GroupsPage } from './features/groups/pages/GroupsPage'
import { GroupDetailPage } from './features/groups/pages/GroupDetailPage'
import { ExpensesPage } from './features/expenses/pages/ExpensesPage'
import { BalancesPage } from './features/balances/pages/BalancesPage'
import { AnalyticsPage } from './features/analytics/pages/AnalyticsPage'
import { SettingsPage } from './features/settings/pages/SettingsPage'

function App() {
  return (
    <ErrorBoundary>
      <Router>
        <GuestModeProvider>
          <AuthProvider>
            <GroupProvider>
              <ExpenseProvider>
                <BalanceProvider>
                  <GuestModeApp />


                  {/* PWA Install Prompt */}
                  <PWAInstallPrompt />

                  {/* Toast Notifications */}
                  <Toaster
                    position="top-right"
                    toastOptions={{
                      duration: 4000,
                      style: {
                        background: '#363636',
                        color: '#fff',
                        borderRadius: '12px',
                        padding: '16px',
                      },
                      success: {
                        style: {
                          background: '#10b981',
                        },
                      },
                      error: {
                        style: {
                          background: '#ef4444',
                        },
                      },
                    }}
                  />
                </BalanceProvider>
              </ExpenseProvider>
            </GroupProvider>
          </AuthProvider>
        </GuestModeProvider>
      </Router>
    </ErrorBoundary>
  )
}

export default App
