import React, { Fragment, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { motion, AnimatePresence } from 'framer-motion'
import { X } from 'lucide-react'
import { Button } from './Button'

// Animation variants for modals
const overlayVariants = {
  initial: { opacity: 0 },
  animate: { opacity: 1 },
  exit: { opacity: 0 }
}

const modalVariants = {
  initial: {
    opacity: 0,
    scale: 0.75,
    y: 20
  },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30
    }
  },
  exit: {
    opacity: 0,
    scale: 0.75,
    y: 20,
    transition: {
      duration: 0.2
    }
  }
}

export function Modal({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  actions,
  className = ''
}) {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-7xl'
  }

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])

  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog
          as={motion.div}
          className="relative z-50"
          onClose={closeOnOverlayClick ? onClose : () => {}}
          static
        >
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black/25 backdrop-blur-sm"
            variants={overlayVariants}
            initial="initial"
            animate="animate"
            exit="exit"
          />

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <motion.div
                variants={modalVariants}
                initial="initial"
                animate="animate"
                exit="exit"
              >
              <Dialog.Panel 
                className={`w-full ${sizeClasses[size]} transform overflow-hidden rounded-2xl bg-white text-left align-middle shadow-xl transition-all ${className}`}
              >
                {/* Header */}
                {(title || showCloseButton) && (
                  <div className="flex items-center justify-between p-6 border-b border-gray-200">
                    {title && (
                      <Dialog.Title
                        as="h3"
                        className="text-lg font-semibold leading-6 text-gray-900"
                      >
                        {title}
                      </Dialog.Title>
                    )}
                    {showCloseButton && (
                      <motion.button
                        type="button"
                        className="rounded-md text-gray-400 hover:text-gray-600"
                        onClick={onClose}
                        whileHover={{ scale: 1.1, rotate: 90 }}
                        whileTap={{ scale: 0.9 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <span className="sr-only">Close</span>
                        <X className="h-6 w-6" />
                      </motion.button>
                    )}
                  </div>
                )}

                {/* Content */}
                <div className={`${title || showCloseButton ? 'p-6' : 'p-6'}`}>
                  {children}
                </div>

                {/* Actions */}
                {actions && (
                  <div className="flex justify-end space-x-3 px-6 py-4 bg-gray-50 border-t border-gray-200">
                    {actions.map((action, index) => (
                      <Button
                        key={index}
                        variant={action.variant || 'secondary'}
                        onClick={action.onClick}
                        disabled={action.disabled}
                        loading={action.loading}
                      >
                        {action.label}
                      </Button>
                    ))}
                  </div>
                )}
              </Dialog.Panel>
              </motion.div>
            </div>
          </div>
        </Dialog>
      )}
    </AnimatePresence>
  )
}

export function ConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmLabel = 'Confirm',
  cancelLabel = 'Cancel',
  variant = 'danger',
  loading = false
}) {
  const actions = [
    {
      label: cancelLabel,
      variant: 'secondary',
      onClick: onClose,
      disabled: loading
    },
    {
      label: confirmLabel,
      variant: variant,
      onClick: onConfirm,
      loading: loading
    }
  ]

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      actions={actions}
      size="sm"
      closeOnOverlayClick={!loading}
    >
      <p className="text-gray-600">{message}</p>
    </Modal>
  )
}

export function FormModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  children,
  submitLabel = 'Save',
  cancelLabel = 'Cancel',
  loading = false,
  disabled = false,
  size = 'md'
}) {
  const handleSubmit = (e) => {
    e.preventDefault()
    onSubmit(e)
  }

  const actions = [
    {
      label: cancelLabel,
      variant: 'secondary',
      onClick: onClose,
      disabled: loading
    },
    {
      label: submitLabel,
      variant: 'primary',
      type: 'submit',
      form: 'modal-form',
      loading: loading,
      disabled: disabled
    }
  ]

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      actions={actions}
      size={size}
      closeOnOverlayClick={!loading}
    >
      <form id="modal-form" onSubmit={handleSubmit}>
        {children}
      </form>
    </Modal>
  )
}
