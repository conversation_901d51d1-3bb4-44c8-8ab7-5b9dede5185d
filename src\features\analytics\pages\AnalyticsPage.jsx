import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  TagIcon,
  TrendingUpIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'
import { useExpenses } from '../../../contexts/ExpenseContext'
import { useGroups } from '../../../contexts/GroupContext'
import { useAuth } from '../../../contexts/AuthContext'
import { Button } from '../../../components/ui/Button'
import { SkeletonStats, SkeletonCard } from '../../../components/ui/SkeletonLoader'
import { formatCurrency } from '../../../utils/formatters'

export function AnalyticsPage() {
  const { expenses } = useExpenses()
  const { groups } = useGroups()
  const { isGuest } = useAuth()
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [selectedGroup, setSelectedGroup] = useState('all')

  // Calculate analytics data
  const getFilteredExpenses = () => {
    let filtered = expenses

    // Filter by group
    if (selectedGroup !== 'all') {
      filtered = filtered.filter(expense => expense.groupId === selectedGroup)
    }

    // Filter by period
    const now = new Date()
    const periodStart = new Date()

    switch (selectedPeriod) {
      case 'week':
        periodStart.setDate(now.getDate() - 7)
        break
      case 'month':
        periodStart.setMonth(now.getMonth() - 1)
        break
      case 'year':
        periodStart.setFullYear(now.getFullYear() - 1)
        break
      default:
        periodStart.setFullYear(2000) // All time
    }

    return filtered.filter(expense => new Date(expense.date) >= periodStart)
  }

  const filteredExpenses = getFilteredExpenses()

  // Calculate summary stats
  const totalExpenses = filteredExpenses.reduce((sum, expense) => sum + expense.amount, 0)
  const averageExpense = filteredExpenses.length > 0 ? totalExpenses / filteredExpenses.length : 0
  const expenseCount = filteredExpenses.length

  // Calculate category breakdown
  const categoryBreakdown = filteredExpenses.reduce((acc, expense) => {
    acc[expense.category] = (acc[expense.category] || 0) + expense.amount
    return acc
  }, {})

  const topCategories = Object.entries(categoryBreakdown)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)

  // Calculate monthly trend (last 6 months)
  const monthlyTrend = []
  for (let i = 5; i >= 0; i--) {
    const date = new Date()
    date.setMonth(date.getMonth() - i)
    const monthStart = new Date(date.getFullYear(), date.getMonth(), 1)
    const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0)

    const monthExpenses = expenses.filter(expense => {
      const expenseDate = new Date(expense.date)
      return expenseDate >= monthStart && expenseDate <= monthEnd &&
             (selectedGroup === 'all' || expense.groupId === selectedGroup)
    })

    monthlyTrend.push({
      month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      amount: monthExpenses.reduce((sum, expense) => sum + expense.amount, 0),
      count: monthExpenses.length
    })
  }

  // Calculate group breakdown
  const groupBreakdown = groups.map(group => {
    const groupExpenses = filteredExpenses.filter(expense => expense.groupId === group.id)
    return {
      name: group.name,
      amount: groupExpenses.reduce((sum, expense) => sum + expense.amount, 0),
      count: groupExpenses.length
    }
  }).filter(group => group.amount > 0)
    .sort((a, b) => b.amount - a.amount)

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="md:flex md:items-center md:justify-between"
      >
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Analytics & Insights
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            {isGuest
              ? "Analyze your spending patterns. Data is stored locally in guest mode."
              : "Get insights into your spending habits and group expenses."
            }
          </p>
        </div>
      </motion.div>

      {/* Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="flex flex-col sm:flex-row gap-4"
      >
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Time Period
          </label>
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
          >
            <option value="week">Last 7 Days</option>
            <option value="month">Last 30 Days</option>
            <option value="year">Last Year</option>
            <option value="all">All Time</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Group
          </label>
          <select
            value={selectedGroup}
            onChange={(e) => setSelectedGroup(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
          >
            <option value="all">All Groups</option>
            {groups.map((group) => (
              <option key={group.id} value={group.id}>
                {group.name}
              </option>
            ))}
          </select>
        </div>
      </motion.div>

      {/* Summary Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4"
      >
        {[
          {
            name: 'Total Spent',
            value: formatCurrency(totalExpenses),
            icon: CurrencyDollarIcon,
            color: 'bg-blue-500',
            change: `${expenseCount} expenses`
          },
          {
            name: 'Average Expense',
            value: formatCurrency(averageExpense),
            icon: ChartBarIcon,
            color: 'bg-green-500',
            change: 'Per expense'
          },
          {
            name: 'Total Expenses',
            value: expenseCount.toString(),
            icon: CalendarIcon,
            color: 'bg-purple-500',
            change: `In ${selectedPeriod === 'all' ? 'all time' : selectedPeriod}`
          },
          {
            name: 'Active Groups',
            value: groupBreakdown.length.toString(),
            icon: UserGroupIcon,
            color: 'bg-indigo-500',
            change: 'With expenses'
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 + index * 0.1 }}
            className="card"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="ml-4 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="text-2xl font-bold text-gray-900">
                    {stat.value}
                  </dd>
                  <dd className="text-sm text-gray-500">
                    {stat.change}
                  </dd>
                </dl>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Category Breakdown */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="card"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Top Categories
          </h3>
          {topCategories.length === 0 ? (
            <div className="text-center py-8">
              <TagIcon className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-500">No expenses found</p>
            </div>
          ) : (
            <div className="space-y-3">
              {topCategories.map(([category, amount], index) => {
                const percentage = (amount / totalExpenses) * 100
                return (
                  <div key={category} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      <div className="w-3 h-3 rounded-full bg-primary-500" style={{
                        backgroundColor: `hsl(${index * 60}, 70%, 50%)`
                      }}></div>
                      <span className="text-sm font-medium text-gray-900">{category}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-gray-900">
                        {formatCurrency(amount)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {percentage.toFixed(1)}%
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </motion.div>

        {/* Monthly Trend */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="card"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Monthly Trend
          </h3>
          {monthlyTrend.every(month => month.amount === 0) ? (
            <div className="text-center py-8">
              <TrendingUpIcon className="mx-auto h-12 w-12 text-gray-400" />
              <p className="mt-2 text-sm text-gray-500">No trend data available</p>
            </div>
          ) : (
            <div className="space-y-3">
              {monthlyTrend.map((month, index) => {
                const maxAmount = Math.max(...monthlyTrend.map(m => m.amount))
                const barWidth = maxAmount > 0 ? (month.amount / maxAmount) * 100 : 0
                return (
                  <div key={month.month} className="flex items-center justify-between">
                    <div className="w-16 text-xs text-gray-500">{month.month}</div>
                    <div className="flex-1 mx-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-primary-500 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${barWidth}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="text-right w-20">
                      <div className="text-sm font-semibold text-gray-900">
                        {formatCurrency(month.amount)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {month.count} expenses
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </motion.div>
      </div>

      {/* Group Breakdown */}
      {groupBreakdown.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="card"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Spending by Group
          </h3>
          <div className="space-y-3">
            {groupBreakdown.map((group, index) => {
              const percentage = (group.amount / totalExpenses) * 100
              return (
                <div key={group.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="w-3 h-3 rounded-full bg-primary-500" style={{
                      backgroundColor: `hsl(${index * 45}, 70%, 50%)`
                    }}></div>
                    <span className="text-sm font-medium text-gray-900">{group.name}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-semibold text-gray-900">
                      {formatCurrency(group.amount)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {percentage.toFixed(1)}% • {group.count} expenses
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </motion.div>
      )}

      {/* No Data State */}
      {filteredExpenses.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-center py-12"
        >
          <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-semibold text-gray-900">No data available</h3>
          <p className="mt-1 text-sm text-gray-500">
            Add some expenses to see analytics and insights.
          </p>
        </motion.div>
      )}
    </div>
  )
}
