import React, { useState } from 'react'
import { useGroups } from '../contexts/GroupContext'
import { useGuestMode } from '../contexts/GuestModeContext'
import { Button } from './ui/Button'
import toast from 'react-hot-toast'

export function TestGroupCreation() {
  const [isCreating, setIsCreating] = useState(false)
  const { createGroup, groups } = useGroups()
  const { isGuestMode, userName } = useGuestMode()

  const handleCreateTestGroup = async () => {
    setIsCreating(true)
    try {
      const testGroup = {
        name: `Test Group ${Date.now()}`,
        description: 'A test group created to verify functionality',
        currency: 'USD',
        members: [
          {
            id: 'test-member-1',
            name: '<PERSON>',
            email: '<EMAIL>',
            role: 'member',
            avatar: null,
            joinedAt: new Date().toISOString()
          }
        ]
      }

      await createGroup(testGroup)
      toast.success('Test group created successfully!')
    } catch (error) {
      console.error('Error creating test group:', error)
      toast.error('Failed to create test group')
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <h3 className="text-lg font-semibold text-yellow-800 mb-2">
        Test Group Creation
      </h3>
      <p className="text-sm text-yellow-700 mb-4">
        Guest Mode: {isGuestMode ? 'Active' : 'Inactive'} | 
        User: {userName} | 
        Groups: {groups.length}
      </p>
      <Button
        onClick={handleCreateTestGroup}
        loading={isCreating}
        disabled={isCreating}
      >
        Create Test Group
      </Button>
    </div>
  )
}
