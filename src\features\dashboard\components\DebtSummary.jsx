import React from 'react'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import {
  TrendingUp,
  TrendingDown,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Calendar,
  Bell
} from 'lucide-react'
import { formatCurrency } from '../../../utils/formatters'
import { useGuestMode } from '../../../contexts/GuestModeContext'

const itemVariants = {
  initial: { opacity: 0, x: -20 },
  animate: { 
    opacity: 1, 
    x: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  }
}

const containerVariants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

export function DebtSummary({ groups = [] }) {
  const navigate = useNavigate()
  const { preferences } = useGuestMode()

  // Calculate overall debt summary
  const debtSummary = React.useMemo(() => {
    if (!Array.isArray(groups) || groups.length === 0) {
      return {
        totalOwed: 0,
        totalOwing: 0,
        netBalance: 0,
        settledGroups: 0,
        totalGroups: 0,
        debts: []
      }
    }

    let totalOwed = 0
    let totalOwing = 0
    let settledGroups = 0
    const debts = []

    try {
      groups.forEach(group => {
        if (!group || !group.balances || typeof group.balances !== 'object') {
          settledGroups++
          return
        }

        let groupOwed = 0
        let groupOwing = 0

        Object.entries(group.balances).forEach(([member, balance]) => {
          const numBalance = Number(balance) || 0
          if (numBalance > 0) {
            groupOwed += numBalance
            debts.push({
              type: 'owed',
              amount: numBalance,
              member,
              group: group.name || 'Unknown Group',
              groupId: group.id,
              dueDate: group.dueDate
            })
          } else if (numBalance < 0) {
            groupOwing += Math.abs(numBalance)
            debts.push({
              type: 'owing',
              amount: Math.abs(numBalance),
              member,
              group: group.name || 'Unknown Group',
              groupId: group.id,
              dueDate: group.dueDate
            })
          }
        })

        if (groupOwed === 0 && groupOwing === 0) {
          settledGroups++
        }

        totalOwed += groupOwed
        totalOwing += groupOwing
      })
    } catch (error) {
      console.error('Error calculating debt summary:', error)
      return {
        totalOwed: 0,
        totalOwing: 0,
        netBalance: 0,
        settledGroups: 0,
        totalGroups: groups.length,
        debts: []
      }
    }

    return {
      totalOwed,
      totalOwing,
      netBalance: totalOwed - totalOwing,
      settledGroups,
      totalGroups: groups.length,
      debts: debts.sort((a, b) => b.amount - a.amount).slice(0, 5) // Top 5 debts
    }
  }, [groups])

  const getNetBalanceColor = () => {
    if (debtSummary.netBalance > 0) return 'text-green-600 dark:text-green-400'
    if (debtSummary.netBalance < 0) return 'text-red-600 dark:text-red-400'
    return 'text-gray-600 dark:text-gray-400'
  }

  const getNetBalanceIcon = () => {
    if (debtSummary.netBalance > 0) return TrendingUp
    if (debtSummary.netBalance < 0) return TrendingDown
    return CheckCircle
  }

  const NetBalanceIcon = getNetBalanceIcon()

  if (groups.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
          Debt Summary
        </h2>
        <div className="text-center py-8">
          <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">
            No debts to track yet. Create a group to get started!
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Debt Summary
        </h2>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => navigate('/balances')}
          className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
        >
          <ArrowRight className="w-5 h-5" />
        </motion.button>
      </div>

      {/* Overall Summary */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {formatCurrency(debtSummary.totalOwed, preferences.currency)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300">You're owed</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">
            {formatCurrency(debtSummary.totalOwing, preferences.currency)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300">You owe</div>
        </div>
        
        <div className="text-center">
          <div className={`text-2xl font-bold flex items-center justify-center ${getNetBalanceColor()}`}>
            <NetBalanceIcon className="w-6 h-6 mr-1" />
            {formatCurrency(Math.abs(debtSummary.netBalance), preferences.currency)}
          </div>
          <div className="text-sm text-gray-600 dark:text-gray-300">
            Net {debtSummary.netBalance >= 0 ? 'owed' : 'owing'}
          </div>
        </div>
      </div>

      {/* Settlement Status */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {debtSummary.settledGroups} of {debtSummary.totalGroups} groups settled
            </span>
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {Math.round((debtSummary.settledGroups / debtSummary.totalGroups) * 100)}%
          </div>
        </div>
        <div className="mt-2 w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <motion.div
            className="bg-green-500 h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${(debtSummary.settledGroups / debtSummary.totalGroups) * 100}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Recent Debts */}
      {debtSummary.debts.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Recent Debts
          </h3>
          <motion.div
            variants={containerVariants}
            initial="initial"
            animate="animate"
            className="space-y-3"
          >
            {debtSummary.debts.map((debt, index) => (
              <motion.div
                key={`${debt.groupId}-${debt.member}-${index}`}
                variants={itemVariants}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer"
                onClick={() => navigate(`/groups/${debt.groupId}`)}
              >
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    debt.type === 'owed' ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {debt.member}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {debt.group}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className={`font-semibold ${
                    debt.type === 'owed' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {debt.type === 'owed' ? '+' : '-'}{formatCurrency(debt.amount, preferences.currency)}
                  </div>
                  {debt.dueDate && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      Due {new Date(debt.dueDate).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      )}

      {/* Action Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => navigate('/balances')}
        className="w-full mt-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center"
      >
        <span>View All Balances</span>
        <ArrowRight className="w-4 h-4 ml-2" />
      </motion.button>
    </div>
  )
}
