<!DOCTYPE html>
<html>
<head>
    <title>Icon Generator</title>
</head>
<body>
    <canvas id="canvas" width="512" height="512" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadIcon(64)">Download 64x64</button>
    <button onclick="downloadIcon(192)">Download 192x192</button>
    <button onclick="downloadIcon(512)">Download 512x512</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        function drawIcon() {
            // Clear canvas
            ctx.clearRect(0, 0, 512, 512);
            
            // Background
            ctx.fillStyle = '#3B82F6';
            ctx.fillRect(0, 0, 512, 512);
            
            // Dollar sign
            ctx.fillStyle = 'white';
            ctx.font = 'bold 300px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('$', 256, 256);
            
            // Split lines
            ctx.strokeStyle = 'white';
            ctx.lineWidth = 8;
            ctx.beginPath();
            ctx.moveTo(128, 240);
            ctx.lineTo(384, 240);
            ctx.moveTo(128, 272);
            ctx.lineTo(384, 272);
            ctx.stroke();
        }
        
        function downloadIcon(size) {
            // Create a new canvas with the desired size
            const newCanvas = document.createElement('canvas');
            newCanvas.width = size;
            newCanvas.height = size;
            const newCtx = newCanvas.getContext('2d');
            
            // Draw the icon scaled to the new size
            newCtx.drawImage(canvas, 0, 0, size, size);
            
            // Download
            const link = document.createElement('a');
            link.download = `pwa-${size}x${size}.png`;
            link.href = newCanvas.toDataURL();
            link.click();
        }
        
        // Draw the icon when page loads
        drawIcon();
    </script>
</body>
</html>
