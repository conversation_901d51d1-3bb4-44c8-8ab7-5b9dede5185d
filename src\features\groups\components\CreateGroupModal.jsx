import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { FormModal } from '../../../components/ui/Modal'
import { Input, TextArea } from '../../../components/ui/Input'
import { Button } from '../../../components/ui/Button'
import { useGroups } from '../../../contexts/GroupContext'
import { useGuestMode } from '../../../contexts/GuestModeContext'
import {
  PhotoIcon,
  UserPlusIcon,
  TrashIcon,
  Plus,
  Users
} from 'lucide-react'
import toast from 'react-hot-toast'

const currencies = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' }
]

export function CreateGroupModal({ isOpen, onClose, onSubmit }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    currency: 'USD',
    image: null,
    members: []
  })
  const [errors, setErrors] = useState({})
  const [isLoading, setIsLoading] = useState(false)
  const [newMemberName, setNewMemberName] = useState('')

  const { createGroup } = useGroups()
  const { preferences, userName } = useGuestMode()

  const handleChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleImageChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      // In a real app, you'd upload this to a service like Cloudinary
      const reader = new FileReader()
      reader.onload = (e) => {
        setFormData(prev => ({
          ...prev,
          image: e.target.result
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const addMember = () => {
    if (!newMemberName.trim()) return

    const newMember = {
      id: Date.now().toString(),
      name: newMemberName.trim(),
      email: `${newMemberName.toLowerCase().replace(/\s+/g, '')}@example.com`,
      role: 'member',
      avatar: null,
      joinedAt: new Date().toISOString()
    }

    setFormData(prev => ({
      ...prev,
      members: [...prev.members, newMember]
    }))
    setNewMemberName('')
  }

  const removeMember = (memberId) => {
    setFormData(prev => ({
      ...prev,
      members: prev.members.filter(member => member.id !== memberId)
    }))
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Group name is required'
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Group name must be at least 2 characters'
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    setIsLoading(true)
    
    try {
      const groupData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        currency: formData.currency,
        image: formData.image,
        members: formData.members
      }

      await createGroup(groupData)
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        currency: preferences.currency || 'USD',
        image: null,
        members: []
      })
      setErrors({})
      setNewMemberName('')
      onClose()
    } catch (error) {
      setErrors({ submit: error.message })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        name: '',
        description: '',
        currency: 'USD',
        image: null
      })
      setErrors({})
      onClose()
    }
  }

  return (
    <FormModal
      isOpen={isOpen}
      onClose={handleClose}
      onSubmit={handleSubmit}
      title="Create New Group"
      submitLabel="Create Group"
      loading={isLoading}
      disabled={!formData.name.trim()}
    >
      <div className="space-y-6">
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-sm text-red-600">{errors.submit}</p>
          </div>
        )}

        {/* Group Image */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Group Image (Optional)
          </label>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
              {formData.image ? (
                <img 
                  src={formData.image} 
                  alt="Group preview"
                  className="w-full h-full object-cover"
                />
              ) : (
                <PhotoIcon className="w-8 h-8 text-gray-400" />
              )}
            </div>
            <div>
              <input
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
                id="group-image"
              />
              <label
                htmlFor="group-image"
                className="cursor-pointer bg-white py-2 px-3 border border-gray-300 rounded-md shadow-sm text-sm leading-4 font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Choose Image
              </label>
              <p className="text-xs text-gray-500 mt-1">
                PNG, JPG up to 2MB
              </p>
            </div>
          </div>
        </div>

        {/* Group Name */}
        <Input
          label="Group Name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          error={errors.name}
          placeholder="e.g., Weekend Trip, Roommates, Office Lunch"
          required
          disabled={isLoading}
        />

        {/* Description */}
        <TextArea
          label="Description"
          name="description"
          value={formData.description}
          onChange={handleChange}
          error={errors.description}
          placeholder="What's this group for? (optional)"
          rows={3}
          disabled={isLoading}
        />

        {/* Currency */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Currency
          </label>
          <select
            name="currency"
            value={formData.currency}
            onChange={handleChange}
            disabled={isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
          >
            {currencies.map((currency) => (
              <option key={currency.code} value={currency.code}>
                {currency.symbol} {currency.name} ({currency.code})
              </option>
            ))}
          </select>
        </div>

        {/* Members Section */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            <Users className="w-4 h-4 inline mr-1" />
            Group Members (Optional)
          </label>

          {/* Add Member Input */}
          <div className="flex gap-2 mb-3">
            <Input
              placeholder="Enter member name"
              value={newMemberName}
              onChange={(e) => setNewMemberName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addMember()}
              disabled={isLoading}
              className="flex-1"
            />
            <Button
              type="button"
              onClick={addMember}
              disabled={!newMemberName.trim() || isLoading}
              leftIcon={<Plus className="w-4 h-4" />}
              variant="outline"
            >
              Add
            </Button>
          </div>

          {/* Members List */}
          {formData.members.length > 0 && (
            <div className="space-y-2 mb-3">
              <p className="text-sm text-gray-600">Added members:</p>
              {formData.members.map((member) => (
                <motion.div
                  key={member.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                >
                  <span className="text-sm font-medium text-gray-700">
                    {member.name}
                  </span>
                  <button
                    type="button"
                    onClick={() => removeMember(member.id)}
                    disabled={isLoading}
                    className="text-red-500 hover:text-red-700 transition-colors"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-sm text-blue-800">
            💡 <strong>Tip:</strong> You'll be added as the group admin automatically.
            You can invite more members later using the group invite link!
          </p>
        </div>
      </div>
    </FormModal>
  )
}
