import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { saveToStorage, loadFromStorage, initializeGuestMode, STORAGE_KEYS } from '../utils/storage'

const GuestModeContext = createContext()

// Action types
const ACTIONS = {
  INITIALIZE: 'INITIALIZE',
  UPDATE_PREFERENCES: 'UPDATE_PREFERENCES',
  SET_ONBOARDING_COMPLETED: 'SET_ONBOARDING_COMPLETED',
  UPDATE_USER_INFO: 'UPDATE_USER_INFO',
  TOGGLE_THEME: 'TOGGLE_THEME',
  SET_CURRENCY: 'SET_CURRENCY',
  RESET_DATA: 'RESET_DATA'
}

// Initial state
const initialState = {
  isGuestMode: true,
  userId: null,
  userName: 'Guest User',
  preferences: {
    currency: 'USD',
    theme: 'light',
    notifications: true,
    language: 'en'
  },
  onboardingCompleted: false,
  isLoading: true
}

// Reducer
const guestModeReducer = (state, action) => {
  switch (action.type) {
    case ACTIONS.INITIALIZE:
      return {
        ...state,
        ...action.payload,
        isLoading: false
      }
    
    case ACTIONS.UPDATE_PREFERENCES:
      return {
        ...state,
        preferences: {
          ...state.preferences,
          ...action.payload
        }
      }
    
    case ACTIONS.SET_ONBOARDING_COMPLETED:
      return {
        ...state,
        onboardingCompleted: action.payload
      }
    
    case ACTIONS.UPDATE_USER_INFO:
      return {
        ...state,
        userName: action.payload.userName || state.userName
      }
    
    case ACTIONS.TOGGLE_THEME:
      return {
        ...state,
        preferences: {
          ...state.preferences,
          theme: state.preferences.theme === 'light' ? 'dark' : 'light'
        }
      }
    
    case ACTIONS.SET_CURRENCY:
      return {
        ...state,
        preferences: {
          ...state.preferences,
          currency: action.payload
        }
      }
    
    case ACTIONS.RESET_DATA:
      return {
        ...initialState,
        isLoading: false
      }
    
    default:
      return state
  }
}

export function GuestModeProvider({ children }) {
  const [state, dispatch] = useReducer(guestModeReducer, initialState)

  // Initialize Guest Mode on mount
  useEffect(() => {
    const initializeApp = async () => {
      try {
        const guestState = initializeGuestMode()
        dispatch({
          type: ACTIONS.INITIALIZE,
          payload: guestState
        })
      } catch (error) {
        console.error('Failed to initialize Guest Mode:', error)
        dispatch({
          type: ACTIONS.INITIALIZE,
          payload: initialState
        })
      }
    }

    initializeApp()
  }, [])

  // Save state changes to localStorage
  useEffect(() => {
    if (!state.isLoading) {
      const stateToSave = {
        isGuestMode: state.isGuestMode,
        userId: state.userId,
        userName: state.userName,
        preferences: state.preferences,
        onboardingCompleted: state.onboardingCompleted
      }
      saveToStorage(STORAGE_KEYS.APP_STATE, stateToSave)
    }
  }, [state])

  // Apply theme to document
  useEffect(() => {
    if (state.preferences.theme === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [state.preferences.theme])

  const updatePreferences = (preferences) => {
    dispatch({
      type: ACTIONS.UPDATE_PREFERENCES,
      payload: preferences
    })
  }

  const completeOnboarding = () => {
    dispatch({
      type: ACTIONS.SET_ONBOARDING_COMPLETED,
      payload: true
    })
  }

  const updateUserInfo = (userInfo) => {
    dispatch({
      type: ACTIONS.UPDATE_USER_INFO,
      payload: userInfo
    })
  }

  const toggleTheme = () => {
    dispatch({
      type: ACTIONS.TOGGLE_THEME
    })
  }

  const setCurrency = (currency) => {
    dispatch({
      type: ACTIONS.SET_CURRENCY,
      payload: currency
    })
  }

  const resetAllData = () => {
    dispatch({
      type: ACTIONS.RESET_DATA
    })
  }

  const value = {
    ...state,
    updatePreferences,
    completeOnboarding,
    updateUserInfo,
    toggleTheme,
    setCurrency,
    resetAllData
  }

  return (
    <GuestModeContext.Provider value={value}>
      {children}
    </GuestModeContext.Provider>
  )
}

export const useGuestMode = () => {
  const context = useContext(GuestModeContext)
  if (!context) {
    throw new Error('useGuestMode must be used within a GuestModeProvider')
  }
  return context
}
