import React, { useState, forwardRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react'

// Animation variants
const inputVariants = {
  initial: { scale: 1 },
  focus: {
    scale: 1.02,
    transition: { type: "spring", stiffness: 300, damping: 20 }
  },
  error: {
    x: [-10, 10, -10, 10, 0],
    transition: { duration: 0.4 }
  }
}

const labelVariants = {
  initial: { y: 0, scale: 1, color: "#6B7280" },
  focus: {
    y: -2,
    scale: 0.95,
    color: "#3B82F6",
    transition: { type: "spring", stiffness: 300, damping: 20 }
  },
  error: {
    color: "#EF4444",
    transition: { duration: 0.2 }
  }
}

const iconVariants = {
  initial: { scale: 1, rotate: 0 },
  hover: {
    scale: 1.1,
    rotate: 5,
    transition: { type: "spring", stiffness: 400, damping: 10 }
  }
}

export const Input = forwardRef(({
  label,
  type = 'text',
  error,
  success,
  helperText,
  leftIcon,
  rightIcon,
  placeholder,
  className = '',
  containerClassName = '',
  required = false,
  disabled = false,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = useState(false)
  const [focused, setFocused] = useState(false)
  const [shouldShake, setShouldShake] = useState(false)

  const isPassword = type === 'password'
  const inputType = isPassword && showPassword ? 'text' : type

  // Trigger shake animation when error changes
  useEffect(() => {
    if (error) {
      setShouldShake(true)
      const timer = setTimeout(() => setShouldShake(false), 400)
      return () => clearTimeout(timer)
    }
  }, [error])
  
  const baseInputClasses = `
    w-full px-3 py-2 border rounded-lg transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    placeholder:text-gray-400
  `
  
  const getInputClasses = () => {
    if (error) {
      return `${baseInputClasses} border-red-300 focus:ring-red-500 pr-10`
    }
    if (success) {
      return `${baseInputClasses} border-green-300 focus:ring-green-500 pr-10`
    }
    return `${baseInputClasses} border-gray-300 ${leftIcon ? 'pl-10' : ''} ${rightIcon || isPassword ? 'pr-10' : ''}`
  }

  return (
    <motion.div
      className={`space-y-1 ${containerClassName}`}
      animate={shouldShake ? "error" : "initial"}
      variants={inputVariants}
    >
      {label && (
        <motion.label
          className="block text-sm font-medium"
          variants={labelVariants}
          animate={error ? "error" : focused ? "focus" : "initial"}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </motion.label>
      )}

      <motion.div
        className="relative"
        variants={inputVariants}
        animate={focused ? "focus" : "initial"}
      >
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <motion.span
              className="text-gray-400"
              variants={iconVariants}
              whileHover="hover"
            >
              {leftIcon}
            </motion.span>
          </div>
        )}

        <motion.input
          ref={ref}
          type={inputType}
          className={`${getInputClasses()} ${className}`}
          placeholder={placeholder}
          disabled={disabled}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          whileFocus={{
            boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
            borderColor: "#3B82F6"
          }}
          {...props}
        />
        
        {/* Right side icons */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          <AnimatePresence mode="wait">
            {error && (
              <motion.div
                key="error"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                exit={{ scale: 0, rotate: 180 }}
                transition={{ type: "spring", stiffness: 400, damping: 20 }}
              >
                <AlertCircle className="w-5 h-5 text-red-500" />
              </motion.div>
            )}

            {success && !error && (
              <motion.div
                key="success"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                exit={{ scale: 0, rotate: 180 }}
                transition={{ type: "spring", stiffness: 400, damping: 20 }}
              >
                <CheckCircle className="w-5 h-5 text-green-500" />
              </motion.div>
            )}

            {isPassword && !error && !success && (
              <motion.button
                key="password"
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-gray-400 hover:text-gray-600"
                variants={iconVariants}
                whileHover="hover"
                whileTap={{ scale: 0.9 }}
              >
                <AnimatePresence mode="wait">
                  {showPassword ? (
                    <motion.div
                      key="hide"
                      initial={{ opacity: 0, rotate: -90 }}
                      animate={{ opacity: 1, rotate: 0 }}
                      exit={{ opacity: 0, rotate: 90 }}
                      transition={{ duration: 0.2 }}
                    >
                      <EyeOff className="w-5 h-5" />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="show"
                      initial={{ opacity: 0, rotate: -90 }}
                      animate={{ opacity: 1, rotate: 0 }}
                      exit={{ opacity: 0, rotate: 90 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Eye className="w-5 h-5" />
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.button>
            )}

            {rightIcon && !error && !success && !isPassword && (
              <motion.span
                key="rightIcon"
                className="text-gray-400"
                variants={iconVariants}
                whileHover="hover"
              >
                {rightIcon}
              </motion.span>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
      
      {/* Helper text and error messages */}
      <AnimatePresence mode="wait">
        {(error || success || helperText) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                {error}
              </p>
            )}

            {success && !error && (
              <p className="text-sm text-green-600 flex items-center">
                <CheckCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                {success}
              </p>
            )}
            
            {helperText && !error && !success && (
              <p className="text-sm text-gray-500">
                {helperText}
              </p>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
})

Input.displayName = 'Input'

export function TextArea({
  label,
  error,
  success,
  helperText,
  placeholder,
  className = '',
  containerClassName = '',
  required = false,
  disabled = false,
  rows = 4,
  ...props
}) {
  const baseClasses = `
    w-full px-3 py-2 border rounded-lg transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    placeholder:text-gray-400 resize-none
  `
  
  const getClasses = () => {
    if (error) {
      return `${baseClasses} border-red-300 focus:ring-red-500`
    }
    if (success) {
      return `${baseClasses} border-green-300 focus:ring-green-500`
    }
    return `${baseClasses} border-gray-300`
  }

  return (
    <div className={`space-y-1 ${containerClassName}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        className={`${getClasses()} ${className}`}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        {...props}
      />
      
      <AnimatePresence mode="wait">
        {(error || success || helperText) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                {error}
              </p>
            )}

            {success && !error && (
              <p className="text-sm text-green-600 flex items-center">
                <CheckCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                {success}
              </p>
            )}
            
            {helperText && !error && !success && (
              <p className="text-sm text-gray-500">
                {helperText}
              </p>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
