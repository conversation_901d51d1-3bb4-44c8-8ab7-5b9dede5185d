import React, { useState, forwardRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Eye, EyeOff, AlertCircle, CheckCircle } from 'lucide-react'

export const Input = forwardRef(({
  label,
  type = 'text',
  error,
  success,
  helperText,
  leftIcon,
  rightIcon,
  placeholder,
  className = '',
  containerClassName = '',
  required = false,
  disabled = false,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = useState(false)
  const [focused, setFocused] = useState(false)

  const isPassword = type === 'password'
  const inputType = isPassword && showPassword ? 'text' : type
  
  const baseInputClasses = `
    w-full px-3 py-2 border rounded-lg transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    placeholder:text-gray-400
  `
  
  const getInputClasses = () => {
    if (error) {
      return `${baseInputClasses} border-red-300 focus:ring-red-500 pr-10`
    }
    if (success) {
      return `${baseInputClasses} border-green-300 focus:ring-green-500 pr-10`
    }
    return `${baseInputClasses} border-gray-300 ${leftIcon ? 'pl-10' : ''} ${rightIcon || isPassword ? 'pr-10' : ''}`
  }

  return (
    <div className={`space-y-1 ${containerClassName}`}>
      {label && (
        <label className={`block text-sm font-medium ${error ? 'text-red-600' : 'text-gray-700'}`}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-400">
              {leftIcon}
            </span>
          </div>
        )}

        <input
          ref={ref}
          type={inputType}
          className={`${getInputClasses()} ${className}`}
          placeholder={placeholder}
          disabled={disabled}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          {...props}
        />
        
        {/* Right side icons */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {error && (
            <AlertCircle className="w-5 h-5 text-red-500" />
          )}

          {success && !error && (
            <CheckCircle className="w-5 h-5 text-green-500" />
          )}

          {isPassword && !error && !success && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              {showPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          )}

          {rightIcon && !error && !success && !isPassword && (
            <span className="text-gray-400">
              {rightIcon}
            </span>
          )}
        </div>
      </div>

      {/* Helper text and error messages */}
      {(error || success || helperText) && (
        <div>
          {error && (
            <p className="text-sm text-red-600 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
              {error}
            </p>
          )}

          {success && !error && (
            <p className="text-sm text-green-600 flex items-center">
              <CheckCircle className="w-4 h-4 mr-1 flex-shrink-0" />
              {success}
            </p>
          )}

          {helperText && !error && !success && (
            <p className="text-sm text-gray-500">
              {helperText}
            </p>
          )}
        </div>
      )}
    </div>
  )
})

Input.displayName = 'Input'

export function TextArea({
  label,
  error,
  success,
  helperText,
  placeholder,
  className = '',
  containerClassName = '',
  required = false,
  disabled = false,
  rows = 4,
  ...props
}) {
  const baseClasses = `
    w-full px-3 py-2 border rounded-lg transition-all duration-200 
    focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    placeholder:text-gray-400 resize-none
  `
  
  const getClasses = () => {
    if (error) {
      return `${baseClasses} border-red-300 focus:ring-red-500`
    }
    if (success) {
      return `${baseClasses} border-green-300 focus:ring-green-500`
    }
    return `${baseClasses} border-gray-300`
  }

  return (
    <div className={`space-y-1 ${containerClassName}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <textarea
        className={`${getClasses()} ${className}`}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        {...props}
      />
      
      <AnimatePresence mode="wait">
        {(error || success || helperText) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                {error}
              </p>
            )}

            {success && !error && (
              <p className="text-sm text-green-600 flex items-center">
                <CheckCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                {success}
              </p>
            )}
            
            {helperText && !error && !success && (
              <p className="text-sm text-gray-500">
                {helperText}
              </p>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
