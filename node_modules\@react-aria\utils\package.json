{"name": "@react-aria/utils", "version": "3.30.0", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "exports": {"source": "./src/index.ts", "types": ["./dist/types.d.ts", "./src/index.ts"], "import": "./dist/import.mjs", "require": "./dist/main.js"}, "types": "dist/types.d.ts", "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@react-aria/ssr": "^3.9.10", "@react-stately/flags": "^3.1.2", "@react-stately/utils": "^3.10.8", "@react-types/shared": "^3.31.0", "@swc/helpers": "^0.5.0", "clsx": "^2.0.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}, "publishConfig": {"access": "public"}, "gitHead": "8b9348ff255e018b2dd9b27e2a45507cadfa1d35"}