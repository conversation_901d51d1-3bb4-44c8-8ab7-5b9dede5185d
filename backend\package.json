{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4", "morgan": "^1.10.1", "multer": "^2.0.2", "node-cron": "^4.2.1", "nodemailer": "^7.0.5", "twilio": "^5.7.3"}, "devDependencies": {"jest": "^30.0.5", "nodemon": "^3.1.10", "supertest": "^7.1.4"}}