import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Users, 
  DollarSign, 
  Calculator, 
  Smartphone, 
  ArrowRight, 
  ArrowLeft,
  Check,
  Star
} from 'lucide-react'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { useGuestMode } from '../../contexts/GuestModeContext'

const onboardingSteps = [
  {
    id: 'welcome',
    title: 'Welcome to EasySplit!',
    subtitle: 'Split expenses effortlessly with friends and family',
    icon: Users,
    content: 'Track shared expenses, calculate splits automatically, and keep everyone happy with transparent debt tracking.',
    features: [
      'Create groups for different occasions',
      'Add expenses and split them fairly',
      'Track who owes what to whom',
      'Set payment reminders'
    ]
  },
  {
    id: 'features',
    title: 'Powerful Features',
    subtitle: 'Everything you need for expense management',
    icon: Calculator,
    content: 'EasySplit makes it easy to manage group expenses with smart calculations and intuitive design.',
    features: [
      'Equal or custom splits',
      'Multiple currency support',
      'Offline-first design',
      'Export reports to PDF/CSV'
    ]
  },
  {
    id: 'guest-mode',
    title: 'Guest Mode Active',
    subtitle: 'Start using immediately, no sign-up required',
    icon: Smartphone,
    content: 'Your data is stored locally on your device. You can always sign up later to sync across devices.',
    features: [
      'No account required',
      'Data stored locally',
      'Full feature access',
      'Optional cloud sync later'
    ]
  },
  {
    id: 'personalize',
    title: 'Personalize Your Experience',
    subtitle: 'Set your preferences to get started',
    icon: Star,
    content: 'Customize EasySplit to match your preferences and start creating your first group.',
    isForm: true
  }
]

const currencies = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' }
]

export function GuestOnboarding({ onComplete }) {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState({
    userName: '',
    currency: 'USD',
    theme: 'light'
  })
  
  const { updateUserInfo, updatePreferences, completeOnboarding } = useGuestMode()
  
  const step = onboardingSteps[currentStep]
  const isLastStep = currentStep === onboardingSteps.length - 1

  const handleNext = () => {
    if (isLastStep) {
      handleComplete()
    } else {
      setCurrentStep(prev => prev + 1)
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(0, prev - 1))
  }

  const handleComplete = () => {
    // Update user info and preferences
    updateUserInfo({ userName: formData.userName || 'Guest User' })
    updatePreferences({ 
      currency: formData.currency,
      theme: formData.theme 
    })
    
    // Mark onboarding as completed
    completeOnboarding()
    
    // Call completion callback
    onComplete()
  }

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const pageVariants = {
    initial: { opacity: 0, x: 50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  }

  const featureVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              Step {currentStep + 1} of {onboardingSteps.length}
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-500">
              {Math.round(((currentStep + 1) / onboardingSteps.length) * 100)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <motion.div
              className="bg-primary-600 h-2 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${((currentStep + 1) / onboardingSteps.length) * 100}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            variants={pageVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={{ duration: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8"
          >
            {/* Icon */}
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="flex justify-center mb-6"
            >
              <div className="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center">
                <step.icon className="w-8 h-8 text-primary-600 dark:text-primary-400" />
              </div>
            </motion.div>

            {/* Title and Subtitle */}
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {step.title}
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                {step.subtitle}
              </p>
            </div>

            {/* Content */}
            <div className="mb-8">
              <p className="text-gray-700 dark:text-gray-300 text-center mb-6">
                {step.content}
              </p>

              {step.isForm ? (
                <div className="space-y-6">
                  <Input
                    label="What should we call you?"
                    placeholder="Enter your name (optional)"
                    value={formData.userName}
                    onChange={(e) => handleFormChange('userName', e.target.value)}
                  />
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Preferred Currency
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      {currencies.map((currency) => (
                        <motion.button
                          key={currency.code}
                          onClick={() => handleFormChange('currency', currency.code)}
                          className={`p-3 rounded-lg border-2 transition-all ${
                            formData.currency === currency.code
                              ? 'border-primary-500 bg-primary-50 dark:bg-primary-900'
                              : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                          }`}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="text-left">
                            <div className="font-medium text-gray-900 dark:text-white">
                              {currency.symbol} {currency.code}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {currency.name}
                            </div>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  {step.features.map((feature, index) => (
                    <motion.div
                      key={feature}
                      variants={featureVariants}
                      initial="initial"
                      animate="animate"
                      transition={{ delay: 0.3 + index * 0.1 }}
                      className="flex items-center space-x-3"
                    >
                      <div className="w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                        <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                      </div>
                      <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center">
              <Button
                variant="ghost"
                onClick={handlePrevious}
                disabled={currentStep === 0}
                leftIcon={<ArrowLeft className="w-4 h-4" />}
              >
                Previous
              </Button>

              <Button
                onClick={handleNext}
                rightIcon={isLastStep ? <Check className="w-4 h-4" /> : <ArrowRight className="w-4 h-4" />}
              >
                {isLastStep ? 'Get Started' : 'Next'}
              </Button>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  )
}
