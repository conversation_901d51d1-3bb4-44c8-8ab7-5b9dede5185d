import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Link } from 'react-router-dom'
import { Menu, Transition } from '@headlessui/react'
import {
  Users,
  DollarSign,
  UserPlus,
  MoreVertical,
  Edit,
  Trash2,
  Share,
  Calendar
} from 'lucide-react'
import { IconButton } from '../../../components/ui/Button'

// Animation variants
const cardVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.95
  },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  },
  hover: {
    y: -4,
    scale: 1.02,
    boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  },
  tap: {
    scale: 0.98
  }
}

const iconVariants = {
  initial: { scale: 1, rotate: 0 },
  hover: {
    scale: 1.1,
    rotate: 5,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
}
import { formatCurrency, formatDate } from '../../../utils/formatters'

const groupColors = [
  'bg-blue-500',
  'bg-green-500', 
  'bg-purple-500',
  'bg-pink-500',
  'bg-indigo-500',
  'bg-yellow-500',
  'bg-red-500',
  'bg-teal-500'
]

export function GroupCard({ group, index, onEdit, onDelete }) {
  const [showMenu, setShowMenu] = useState(false)
  
  const colorClass = groupColors[index % groupColors.length]
  const totalExpenses = group.totalExpenses || 0
  const memberCount = group.totalMembers || group.members?.length || 0

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Join ${group.name} on EasySplit`,
          text: `You've been invited to join the expense group "${group.name}"`,
          url: `${window.location.origin}/groups/join/${group.inviteCode || group.id}`
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      const shareUrl = `${window.location.origin}/groups/join/${group.inviteCode || group.id}`
      navigator.clipboard.writeText(shareUrl)
      toast.success('Invite link copied to clipboard!')
    }
  }

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      whileTap="tap"
      className="card-hover group cursor-pointer"
    >
      <Link to={`/groups/${group.id}`} className="block">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`w-12 h-12 ${colorClass} rounded-lg flex items-center justify-center`}>
              {group.image ? (
                <img 
                  src={group.image} 
                  alt={group.name}
                  className="w-full h-full rounded-lg object-cover"
                />
              ) : (
                <Users className="w-6 h-6 text-white" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {group.name}
              </h3>
              {group.description && (
                <p className="text-sm text-gray-500 truncate">
                  {group.description}
                </p>
              )}
            </div>
          </div>
          
          <Menu as="div" className="relative">
            <Menu.Button
              as={IconButton}
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <MoreVertical className="w-5 h-5" />
            </Menu.Button>
            
            <Transition
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        onEdit()
                      }}
                      className={`${
                        active ? 'bg-gray-50' : ''
                      } flex w-full items-center px-4 py-2 text-sm text-gray-700`}
                    >
                      <Edit className="mr-3 h-4 w-4" />
                      Edit Group
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        handleShare()
                      }}
                      className={`${
                        active ? 'bg-gray-50' : ''
                      } flex w-full items-center px-4 py-2 text-sm text-gray-700`}
                    >
                      <Share className="mr-3 h-4 w-4" />
                      Share Invite
                    </button>
                  )}
                </Menu.Item>
                <Menu.Item>
                  {({ active }) => (
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                        onDelete()
                      }}
                      className={`${
                        active ? 'bg-gray-50' : ''
                      } flex w-full items-center px-4 py-2 text-sm text-red-600`}
                    >
                      <Trash2 className="mr-3 h-4 w-4" />
                      Delete Group
                    </button>
                  )}
                </Menu.Item>
              </Menu.Items>
            </Transition>
          </Menu>
        </div>
      </Link>

      <div className="space-y-3">
        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-4 h-4 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {formatCurrency(totalExpenses)}
              </p>
              <p className="text-xs text-gray-500">Total Expenses</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <UserPlus className="w-4 h-4 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {memberCount}
              </p>
              <p className="text-xs text-gray-500">
                {memberCount === 1 ? 'Member' : 'Members'}
              </p>
            </div>
          </div>
        </div>

        {/* Members Preview */}
        {group.members && group.members.length > 0 && (
          <div>
            <p className="text-xs text-gray-500 mb-2">Members</p>
            <div className="flex -space-x-2">
              {group.members.slice(0, 4).map((member, idx) => (
                <div
                  key={member.id}
                  className="w-8 h-8 rounded-full bg-gray-300 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-700"
                  title={member.name}
                >
                  {member.avatar ? (
                    <img 
                      src={member.avatar} 
                      alt={member.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    member.name?.charAt(0)?.toUpperCase() || '?'
                  )}
                </div>
              ))}
              {group.members.length > 4 && (
                <div className="w-8 h-8 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-500">
                  +{group.members.length - 4}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Created Date */}
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <Calendar className="w-4 h-4" />
          <span>Created {formatDate(group.createdAt)}</span>
        </div>
      </div>
    </motion.div>
  )
}
