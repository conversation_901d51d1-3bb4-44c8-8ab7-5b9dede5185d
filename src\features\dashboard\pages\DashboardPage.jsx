import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useNavigate } from 'react-router-dom'
import {
  DollarSign,
  Users,
  Scale,
  BarChart3,
  Plus,
  TrendingUp,
  Activity,
  ArrowRight,
  Calendar,
  AlertCircle
} from 'lucide-react'
import { SkeletonStats, SkeletonCard } from '../../../components/ui/SkeletonLoader'
import { Button, FloatingActionButton } from '../../../components/ui/Button'
import { useAuth } from '../../../contexts/AuthContext'
import { useGroups } from '../../../contexts/GroupContext'
import { formatCurrency } from '../../../utils/formatters'
import { useGuestMode } from '../../../contexts/GuestModeContext'
import { loadFromStorage, saveToStorage, STORAGE_KEYS } from '../../../utils/storage'

// Animation variants
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
}

const containerVariants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

const itemVariants = {
  initial: { opacity: 0, y: 20, scale: 0.95 },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  }
}

const statsVariants = {
  initial: { opacity: 0, scale: 0.8, y: 30 },
  animate: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  },
  hover: {
    scale: 1.02,
    y: -2,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 10
    }
  }
}

const buttonVariants = {
  initial: { scale: 1 },
  hover: {
    scale: 1.05,
    transition: { type: "spring", stiffness: 400, damping: 10 }
  },
  tap: { scale: 0.95 }
}

export function DashboardPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [showCreateGroupModal, setShowCreateGroupModal] = useState(false)
  const { user, isGuest } = useAuth()
  const { groups } = useGroups()
  const { userName, preferences } = useGuestMode()
  const navigate = useNavigate()

  useEffect(() => {
    // Load data from localStorage for Guest Mode
    const loadGuestData = async () => {
      try {
        const savedGroups = loadFromStorage(STORAGE_KEYS.GROUPS, [])
        // Initialize groups if needed
        setIsLoading(false)
      } catch (error) {
        console.error('Failed to load guest data:', error)
        setIsLoading(false)
      }
    }

    loadGuestData()
  }, [])

  // Calculate stats from groups data
  const stats = [
    {
      name: 'Total Groups',
      value: groups.length.toString(),
      icon: Users,
      color: 'bg-blue-500',
      change: groups.length > 0 ? `${groups.length} active` : 'No groups yet'
    },
    {
      name: 'Total Expenses',
      value: formatCurrency(groups.reduce((total, group) => total + (group.totalExpenses || 0), 0)),
      icon: DollarSign,
      color: 'bg-green-500',
      change: 'Across all groups'
    },
    {
      name: 'Total Members',
      value: groups.reduce((total, group) => total + (group.totalMembers || 0), 0).toString(),
      icon: Scale,
      color: 'bg-purple-500',
      change: 'In all groups'
    },
    {
      name: 'Active Groups',
      value: groups.filter(group => group.totalExpenses > 0).length.toString(),
      icon: BarChart3,
      color: 'bg-indigo-500',
      change: 'With expenses'
    }
  ]

  const handleAddExpense = () => {
    // Navigate to add expense page
    console.log('Add expense clicked')
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="md:flex md:items-center md:justify-between">
          <div className="min-w-0 flex-1">
            <div className="h-8 bg-gray-200 rounded w-48 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-64"></div>
          </div>
        </div>
        <SkeletonStats />
        <SkeletonCard />
      </div>
    )
  }

  return (
    <motion.div
      className="space-y-6"
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      {/* Header */}
      <motion.div
        variants={itemVariants}
        className="md:flex md:items-center md:justify-between"
      >
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
            Welcome back, {user?.name?.split(' ')[0] || 'User'}! 👋
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            {isGuest ?
              "You're in guest mode. Your data is stored locally." :
              "Here's an overview of your expenses and groups."
            }
          </p>
        </div>
        <div className="mt-4 flex md:ml-4 md:mt-0">
          <motion.div variants={buttonVariants} whileHover="hover" whileTap="tap">
            <Button
              onClick={handleAddExpense}
              leftIcon={<Plus className="w-5 h-5" />}
            >
              Add Expense
            </Button>
          </motion.div>
        </div>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4"
        variants={containerVariants}
        initial="initial"
        animate="animate"
      >
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            variants={statsVariants}
            whileHover="hover"
            className="card-hover cursor-pointer"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`${stat.color} p-3 rounded-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              <div className="ml-4 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="text-lg font-semibold text-gray-900">
                    {stat.value}
                  </dd>
                  <dd className="text-sm text-gray-600">
                    {stat.change}
                  </dd>
                </dl>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Recent Activity */}
      <motion.div
        variants={itemVariants}
        className="card"
      >
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
            <Button variant="ghost" size="sm">
              View All
            </Button>
          </div>

          <motion.div
            className="space-y-4"
            variants={containerVariants}
            initial="initial"
            animate="animate"
          >
            {[
              {
                icon: DollarSign,
                iconBg: 'bg-green-100',
                iconColor: 'text-green-600',
                title: 'John Doe',
                action: 'added expense "Dinner at Restaurant"',
                time: '2 hours ago',
                amount: '$45.50'
              },
              {
                icon: Users,
                iconBg: 'bg-blue-100',
                iconColor: 'text-blue-600',
                title: 'Sarah Smith',
                action: 'joined group "Weekend Trip"',
                time: '5 hours ago'
              },
              {
                icon: Scale,
                iconBg: 'bg-purple-100',
                iconColor: 'text-purple-600',
                title: 'Mike Johnson',
                action: 'settled up with you',
                time: '1 day ago',
                amount: '$23.75'
              }
            ].map((activity, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{
                  scale: 1.02,
                  backgroundColor: "rgba(249, 250, 251, 1)",
                  transition: { duration: 0.2 }
                }}
                className="flex items-center space-x-3 p-3 rounded-lg cursor-pointer"
              >
                <div className={`flex-shrink-0 w-8 h-8 ${activity.iconBg} rounded-full flex items-center justify-center`}>
                  <activity.icon className={`w-4 h-4 ${activity.iconColor}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-900">
                    <span className="font-medium">{activity.title}</span> {activity.action}
                  </p>
                  <p className="text-sm text-gray-500">{activity.time}</p>
                </div>
                {activity.amount && (
                  <div className="text-sm text-gray-900 font-medium">{activity.amount}</div>
                )}
              </motion.div>
            ))}
          </motion.div>

          {isGuest && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1 }}
              className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"
            >
              <p className="text-sm text-blue-800">
                💡 <strong>Tip:</strong> Create an account to sync your data across devices and never lose your expenses!
              </p>
            </motion.div>
          )}
        </div>
      </motion.div>

      {/* Floating Action Button */}
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{
          type: "spring",
          stiffness: 260,
          damping: 20,
          delay: 0.8
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <FloatingActionButton onClick={handleAddExpense}>
          <Plus className="w-6 h-6" />
        </FloatingActionButton>
      </motion.div>
    </motion.div>
  )
}
