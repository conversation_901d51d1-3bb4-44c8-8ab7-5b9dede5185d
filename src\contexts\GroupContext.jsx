import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { useAuth } from './AuthContext'
import { groupService } from '../services/groupService'
import { useGuestMode } from './GuestModeContext'
import { saveToStorage, loadFromStorage, STORAGE_KEYS } from '../utils/storage'
import toast from 'react-hot-toast'

const GroupContext = createContext()

const initialState = {
  groups: [],
  currentGroup: null,
  isLoading: false,
  error: null
}

function groupReducer(state, action) {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false }
    case 'SET_GROUPS':
      return { ...state, groups: action.payload, isLoading: false, error: null }
    case 'ADD_GROUP':
      return { 
        ...state, 
        groups: [...state.groups, action.payload], 
        isLoading: false, 
        error: null 
      }
    case 'UPDATE_GROUP':
      return {
        ...state,
        groups: state.groups.map(group => 
          group.id === action.payload.id ? action.payload : group
        ),
        currentGroup: state.currentGroup?.id === action.payload.id ? action.payload : state.currentGroup,
        isLoading: false,
        error: null
      }
    case 'DELETE_GROUP':
      return {
        ...state,
        groups: state.groups.filter(group => group.id !== action.payload),
        currentGroup: state.currentGroup?.id === action.payload ? null : state.currentGroup,
        isLoading: false,
        error: null
      }
    case 'SET_CURRENT_GROUP':
      return { ...state, currentGroup: action.payload }
    case 'ADD_MEMBER':
      return {
        ...state,
        currentGroup: state.currentGroup ? {
          ...state.currentGroup,
          members: [...state.currentGroup.members, action.payload]
        } : null
      }
    case 'REMOVE_MEMBER':
      return {
        ...state,
        currentGroup: state.currentGroup ? {
          ...state.currentGroup,
          members: state.currentGroup.members.filter(member => member.id !== action.payload)
        } : null
      }
    default:
      return state
  }
}

export function GroupProvider({ children }) {
  const [state, dispatch] = useReducer(groupReducer, initialState)
  const { user } = useAuth()
  const { isGuestMode, userName } = useGuestMode()

  useEffect(() => {
    // Only load groups once when context initializes
    if (state.groups.length === 0 && !state.loading) {
      loadGroups()
    }
  }, [loadGroups, state.groups.length, state.loading])

  const loadGroups = React.useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })

      if (isGuestMode) {
        // Load groups from localStorage
        const groups = loadFromStorage(STORAGE_KEYS.GROUPS, [])
        dispatch({ type: 'SET_GROUPS', payload: groups })
      } else {
        // Load groups from API
        const groups = await groupService.getGroups()
        dispatch({ type: 'SET_GROUPS', payload: groups })
      }
    } catch (error) {
      console.error('Error loading groups:', error)
      dispatch({ type: 'SET_ERROR', payload: error.message })
      // Don't show error toast in guest mode for initial load
      if (!isGuestMode) {
        toast.error('Failed to load groups')
      }
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [isGuestMode])

  const createGroup = React.useCallback(async (groupData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })

      if (isGuestMode) {
        // Create group in guest mode
        const newGroup = {
          id: Date.now().toString(),
          ...groupData,
          createdAt: new Date().toISOString(),
          lastActivity: new Date().toISOString(),
          balances: {},
          expenses: [],
          members: [
            {
              id: 'current-user',
              name: userName || 'Guest User',
              email: '<EMAIL>',
              role: 'admin',
              avatar: null,
              joinedAt: new Date().toISOString()
            },
            ...(groupData.members || [])
          ],
          totalExpenses: 0,
          totalMembers: 1
        }
        
        const updatedGroups = [...state.groups, newGroup]
        saveToStorage(STORAGE_KEYS.GROUPS, updatedGroups)
        dispatch({ type: 'ADD_GROUP', payload: newGroup })
        toast.success('Group created successfully!')
        return newGroup
      } else {
        // Create group via API
        const newGroup = await groupService.createGroup(groupData)
        dispatch({ type: 'ADD_GROUP', payload: newGroup })
        toast.success('Group created successfully!')
        return newGroup
      }
    } catch (error) {
      console.error('Error creating group:', error)
      dispatch({ type: 'SET_ERROR', payload: error.message })
      toast.error('Failed to create group')
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [isGuestMode, userName, state.groups])

  const updateGroup = async (groupId, groupData) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      
      if (isGuest) {
        // Update group in guest mode
        const updatedGroups = state.groups.map(group =>
          group.id === groupId ? { ...group, ...groupData } : group
        )
        updateGuestData({ groups: updatedGroups })
        const updatedGroup = updatedGroups.find(g => g.id === groupId)
        dispatch({ type: 'UPDATE_GROUP', payload: updatedGroup })
        toast.success('Group updated successfully!')
        return updatedGroup
      } else {
        // Update group via API
        const updatedGroup = await groupService.updateGroup(groupId, groupData)
        dispatch({ type: 'UPDATE_GROUP', payload: updatedGroup })
        toast.success('Group updated successfully!')
        return updatedGroup
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message })
      toast.error('Failed to update group')
      throw error
    }
  }

  const deleteGroup = async (groupId) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      
      if (isGuest) {
        // Delete group in guest mode
        const updatedGroups = state.groups.filter(group => group.id !== groupId)
        updateGuestData({ groups: updatedGroups })
        dispatch({ type: 'DELETE_GROUP', payload: groupId })
        toast.success('Group deleted successfully!')
      } else {
        // Delete group via API
        await groupService.deleteGroup(groupId)
        dispatch({ type: 'DELETE_GROUP', payload: groupId })
        toast.success('Group deleted successfully!')
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message })
      toast.error('Failed to delete group')
      throw error
    }
  }

  const addMember = async (groupId, memberData) => {
    try {
      if (isGuest) {
        // Add member in guest mode
        const newMember = {
          id: Date.now().toString(),
          ...memberData,
          joinedAt: new Date().toISOString(),
          role: 'member'
        }
        
        const updatedGroups = state.groups.map(group =>
          group.id === groupId 
            ? { 
                ...group, 
                members: [...group.members, newMember],
                totalMembers: group.members.length + 1
              }
            : group
        )
        updateGuestData({ groups: updatedGroups })
        dispatch({ type: 'ADD_MEMBER', payload: newMember })
        toast.success('Member added successfully!')
        return newMember
      } else {
        // Add member via API
        const newMember = await groupService.addMember(groupId, memberData)
        dispatch({ type: 'ADD_MEMBER', payload: newMember })
        toast.success('Member added successfully!')
        return newMember
      }
    } catch (error) {
      toast.error('Failed to add member')
      throw error
    }
  }

  const removeMember = async (groupId, memberId) => {
    try {
      if (isGuest) {
        // Remove member in guest mode
        const updatedGroups = state.groups.map(group =>
          group.id === groupId 
            ? { 
                ...group, 
                members: group.members.filter(m => m.id !== memberId),
                totalMembers: group.members.length - 1
              }
            : group
        )
        updateGuestData({ groups: updatedGroups })
        dispatch({ type: 'REMOVE_MEMBER', payload: memberId })
        toast.success('Member removed successfully!')
      } else {
        // Remove member via API
        await groupService.removeMember(groupId, memberId)
        dispatch({ type: 'REMOVE_MEMBER', payload: memberId })
        toast.success('Member removed successfully!')
      }
    } catch (error) {
      toast.error('Failed to remove member')
      throw error
    }
  }

  const setCurrentGroup = (group) => {
    dispatch({ type: 'SET_CURRENT_GROUP', payload: group })
  }

  const getGroupById = (groupId) => {
    return state.groups.find(group => group.id === groupId)
  }

  const value = {
    ...state,
    createGroup,
    updateGroup,
    deleteGroup,
    addMember,
    removeMember,
    setCurrentGroup,
    getGroupById,
    loadGroups
  }

  return (
    <GroupContext.Provider value={value}>
      {children}
    </GroupContext.Provider>
  )
}

export function useGroups() {
  const context = useContext(GroupContext)
  if (!context) {
    throw new Error('useGroups must be used within a GroupProvider')
  }
  return context
}
