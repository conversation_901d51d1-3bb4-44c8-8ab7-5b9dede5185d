import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import { useGuestMode } from '../contexts/GuestModeContext'
import { GuestOnboarding } from './onboarding/GuestOnboarding'
import { Layout } from './layout/Layout'

// Import pages
import { DashboardPage } from '../features/dashboard/pages/DashboardPage'
import { GroupsPage } from '../features/groups/pages/GroupsPage'
import { GroupDetailPage } from '../features/groups/pages/GroupDetailPage'
import { ExpensesPage } from '../features/expenses/pages/ExpensesPage'
import { BalancesPage } from '../features/balances/pages/BalancesPage'
import { AnalyticsPage } from '../features/analytics/pages/AnalyticsPage'
import { SettingsPage } from '../features/settings/pages/SettingsPage'

// Loading component
function LoadingScreen() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          className="w-16 h-16 border-4 border-primary-200 border-t-primary-600 rounded-full mx-auto mb-4"
        />
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-2">
          Loading EasySplit
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Setting up your expense tracker...
        </p>
      </motion.div>
    </div>
  )
}

export function GuestModeApp() {
  const { isLoading, onboardingCompleted } = useGuestMode()

  // Show loading screen while initializing
  if (isLoading) {
    return <LoadingScreen />
  }

  // Show onboarding if not completed
  if (!onboardingCompleted) {
    return (
      <GuestOnboarding 
        onComplete={() => {
          // Onboarding completion is handled by the component
          console.log('Onboarding completed')
        }} 
      />
    )
  }

  // Main app with Guest Mode features
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <AnimatePresence mode="wait">
        <Routes>
          {/* Main app routes */}
          <Route path="/" element={<Layout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="groups" element={<GroupsPage />} />
            <Route path="groups/:id" element={<GroupDetailPage />} />
            <Route path="expenses" element={<ExpensesPage />} />
            <Route path="balances" element={<BalancesPage />} />
            <Route path="analytics" element={<AnalyticsPage />} />
            <Route path="settings" element={<SettingsPage />} />
          </Route>

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </AnimatePresence>
    </div>
  )
}
